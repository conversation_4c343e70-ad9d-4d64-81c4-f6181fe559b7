import { act, renderHook, waitFor } from "@testing-library/react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

// Mock the notification service BEFORE importing
vi.mock("@/lib/tauri-notifications", () => ({
	tauriNotifications: {
		checkPermission: vi.fn(),
		loadSettings: vi.fn(),
		requestPermission: vi.fn(),
		saveSettings: vi.fn(),
		notifyNewMessage: vi.fn(),
		notifyContactRequest: vi.fn(),
		notifyGroupInvite: vi.fn(),
		clearAllNotifications: vi.fn(),
	},
	TauriNotificationService: {
		isTauriEnvironment: vi.fn(),
	},
}));

import {
	TauriNotificationService,
	tauriNotifications,
} from "@/lib/tauri-notifications";
import { useNotifications } from "./useNotifications";

// Get typed mock references
const mockTauriNotifications = vi.mocked(tauriNotifications);
const mockIsTauriEnvironment = vi.mocked(
	TauriNotificationService.isTauriEnvironment,
);

describe("useNotifications", () => {
	beforeEach(() => {
		vi.clearAllMocks();
		mockIsTauriEnvironment.mockReturnValue(true);
		mockTauriNotifications.checkPermission.mockResolvedValue("granted");
		mockTauriNotifications.loadSettings.mockResolvedValue({
			enabled: true,
			soundEnabled: true,
			showPreview: true,
			suppressWhenFocused: true,
			quietHoursEnabled: false,
		});
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe("Initialization", () => {
		it("should initialize with correct default values", () => {
			const { result } = renderHook(() => useNotifications());

			expect(result.current.permission).toBe("denied");
			expect(result.current.settings).toBe(null);
			expect(result.current.isSupported).toBe(true);
			expect(result.current.isLoading).toBe(true);
			expect(result.current.error).toBe(null);
		});

		it("should load permission and settings on mount", async () => {
			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			expect(mockTauriNotifications.checkPermission).toHaveBeenCalled();
			expect(mockTauriNotifications.loadSettings).toHaveBeenCalled();
			expect(result.current.permission).toBe("granted");
			expect(result.current.settings).toEqual({
				enabled: true,
				soundEnabled: true,
				showPreview: true,
				suppressWhenFocused: true,
				quietHoursEnabled: false,
			});
		});

		it("should handle initialization errors", async () => {
			mockTauriNotifications.checkPermission.mockRejectedValue(
				new Error("Permission check failed"),
			);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			expect(result.current.error).toBe("Permission check failed");
		});

		it("should handle non-Tauri environment", () => {
			mockIsTauriEnvironment.mockReturnValue(false);

			const { result } = renderHook(() => useNotifications());

			expect(result.current.isSupported).toBe(false);
			expect(result.current.isLoading).toBe(false);
		});
	});

	describe("Permission Management", () => {
		it("should request permission successfully", async () => {
			mockTauriNotifications.requestPermission.mockResolvedValue("granted");

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			let newPermission: string;
			await act(async () => {
				newPermission = await result.current.requestPermission();
			});

			expect(mockTauriNotifications.requestPermission).toHaveBeenCalled();
			expect(newPermission!).toBe("granted");
			expect(result.current.permission).toBe("granted");
		});

		it("should handle permission request errors", async () => {
			mockTauriNotifications.requestPermission.mockRejectedValue(
				new Error("Permission denied"),
			);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			let newPermission: string;
			await act(async () => {
				newPermission = await result.current.requestPermission();
			});

			expect(newPermission!).toBe("denied");
			expect(result.current.error).toBe("Permission denied");
		});

		it("should return denied for non-supported environment", async () => {
			mockIsTauriEnvironment.mockReturnValue(false);

			const { result } = renderHook(() => useNotifications());

			let permission: string;
			await act(async () => {
				permission = await result.current.requestPermission();
			});

			expect(permission!).toBe("denied");
			expect(mockTauriNotifications.requestPermission).not.toHaveBeenCalled();
		});
	});

	describe("Settings Management", () => {
		it("should update settings successfully", async () => {
			mockTauriNotifications.saveSettings.mockResolvedValue(undefined);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			const newSettings = {
				enabled: false,
				soundEnabled: false,
				showPreview: false,
				suppressWhenFocused: false,
				quietHoursEnabled: true,
				quietHoursStart: "22:00",
				quietHoursEnd: "08:00",
			};

			await act(async () => {
				await result.current.updateSettings(newSettings);
			});

			expect(mockTauriNotifications.saveSettings).toHaveBeenCalledWith(
				newSettings,
			);
			expect(result.current.settings).toEqual(newSettings);
		});

		it("should handle settings update errors", async () => {
			mockTauriNotifications.saveSettings.mockRejectedValue(
				new Error("Save failed"),
			);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			const newSettings = {
				enabled: false,
				soundEnabled: false,
				showPreview: false,
				suppressWhenFocused: false,
				quietHoursEnabled: false,
			};

			await act(async () => {
				await expect(
					result.current.updateSettings(newSettings),
				).rejects.toThrow("Save failed");
			});

			expect(result.current.error).toBe("Save failed");
		});
	});

	describe("Notification Methods", () => {
		beforeEach(async () => {
			mockTauriNotifications.notifyNewMessage.mockResolvedValue(undefined);
			mockTauriNotifications.notifyContactRequest.mockResolvedValue(undefined);
			mockTauriNotifications.notifyGroupInvite.mockResolvedValue(undefined);
		});

		it("should send message notification when conditions are met", async () => {
			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.notifyNewMessage(
					"msg-123",
					"Alice",
					"Hello!",
					"chat-456",
					"user-789",
				);
			});

			expect(mockTauriNotifications.notifyNewMessage).toHaveBeenCalledWith(
				"msg-123",
				"Alice",
				"Hello!",
				"chat-456",
				"user-789",
			);
		});

		it("should not send notification when permission is denied", async () => {
			mockTauriNotifications.checkPermission.mockResolvedValue("denied");

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.notifyNewMessage(
					"msg-123",
					"Alice",
					"Hello!",
					"chat-456",
					"user-789",
				);
			});

			expect(mockTauriNotifications.notifyNewMessage).not.toHaveBeenCalled();
		});

		it("should not send notification when notifications are disabled", async () => {
			mockTauriNotifications.loadSettings.mockResolvedValue({
				enabled: false,
				soundEnabled: true,
				showPreview: true,
				suppressWhenFocused: true,
				quietHoursEnabled: false,
			});

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.notifyNewMessage(
					"msg-123",
					"Alice",
					"Hello!",
					"chat-456",
					"user-789",
				);
			});

			expect(mockTauriNotifications.notifyNewMessage).not.toHaveBeenCalled();
		});

		it("should send contact request notification", async () => {
			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.notifyContactRequest(
					"req-123",
					"Bob Smith",
					"<EMAIL>",
				);
			});

			expect(mockTauriNotifications.notifyContactRequest).toHaveBeenCalledWith(
				"req-123",
				"Bob Smith",
				"<EMAIL>",
			);
		});

		it("should send group invite notification", async () => {
			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.notifyGroupInvite(
					"invite-123",
					"Team Chat",
					"Charlie",
				);
			});

			expect(mockTauriNotifications.notifyGroupInvite).toHaveBeenCalledWith(
				"invite-123",
				"Team Chat",
				"Charlie",
			);
		});

		it("should handle notification errors gracefully", async () => {
			mockTauriNotifications.notifyNewMessage.mockRejectedValue(
				new Error("Notification failed"),
			);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			// Should not throw error
			await act(async () => {
				await result.current.notifyNewMessage(
					"msg-123",
					"Alice",
					"Hello!",
					"chat-456",
					"user-789",
				);
			});

			expect(mockTauriNotifications.notifyNewMessage).toHaveBeenCalled();
		});
	});

	describe("Utility Methods", () => {
		it("should clear all notifications", async () => {
			mockTauriNotifications.clearAllNotifications.mockResolvedValue(undefined);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await result.current.clearAllNotifications();
			});

			expect(mockTauriNotifications.clearAllNotifications).toHaveBeenCalled();
		});

		it("should handle clear notifications error", async () => {
			mockTauriNotifications.clearAllNotifications.mockRejectedValue(
				new Error("Clear failed"),
			);

			const { result } = renderHook(() => useNotifications());

			await waitFor(() => {
				expect(result.current.isLoading).toBe(false);
			});

			await act(async () => {
				await expect(result.current.clearAllNotifications()).rejects.toThrow(
					"Clear failed",
				);
			});
		});
	});
});
