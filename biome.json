{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": true, "includes": ["src/**/*", "convex/**/*", "!src/components/ui/**/*", "!convex/_generated/**/*"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": {"level": "warn", "options": {"attributes": ["className"], "functions": ["clsx", "cva", "tw", "tw.*", "cn"]}, "fix": "safe"}}, "a11y": {"noStaticElementInteractions": {"level": "off", "options": {}}, "useKeyWithClickEvents": {"level": "warn", "options": {}}}, "style": {"useConst": {"level": "error", "options": {}}, "useTemplate": {"level": "warn", "options": {}}, "noNegationElse": {"level": "warn", "options": {}}, "useCollapsedElseIf": {"level": "warn", "options": {}}}, "complexity": {"noUselessFragments": {"level": "warn", "options": {}}}, "correctness": {"useExhaustiveDependencies": {"level": "warn", "options": {}}, "noUnusedVariables": {"level": "error", "options": {}}, "noUnusedImports": {"level": "error", "options": {}}}, "suspicious": {"noExplicitAny": {"level": "error", "options": {}}, "noArrayIndexKey": {"level": "warn", "options": {}}}, "performance": {"noAccumulatingSpread": {"level": "warn", "options": {}}}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}, "json": {"linter": {"enabled": false}, "formatter": {"enabled": false}}}